// Import the Ghost Admin API directly to avoid Obsidian dependencies
// @ts-ignore
import GhostAdminAPI from '@tryghost/admin-api';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Load environment settings from .env file and process.env
 */
export function loadEnvironmentSettings(): { ghostUrl?: string; ghostAdminApiKey?: string } {
  const envSettings: { ghostUrl?: string; ghostAdminApiKey?: string } = {};

  // Try to read from .env file
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=').trim();
            // Remove quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            if (key.trim() === 'GHOST_URL') {
              envSettings.ghostUrl = value;
            } else if (key.trim() === 'GHOST_ADMIN_API_KEY') {
              envSettings.ghostAdminApiKey = value;
            }
          }
        }
      }
    } catch (error) {
      console.log('⚠️ Could not read .env file:', error.message);
    }
  }

  // Override with process.env if available
  if (process.env.GHOST_URL) {
    envSettings.ghostUrl = process.env.GHOST_URL;
  }
  if (process.env.GHOST_ADMIN_API_KEY) {
    envSettings.ghostAdminApiKey = process.env.GHOST_ADMIN_API_KEY;
  }

  return envSettings;
}

/**
 * Enhanced Ghost API client for test operations
 */
export class GhostAPIClient {
  private api: any;

  constructor(url: string, key: string) {
    this.api = new GhostAdminAPI({
      url: url.replace(/\/$/, ''), // Remove trailing slash
      key: key,
      version: 'v6.0'
    });
  }

  async getPosts(options: any = {}): Promise<any[]> {
    try {
      return await this.api.posts.browse(options);
    } catch (error) {
      console.error('Failed to get posts:', error);
      throw error;
    }
  }

  async getPostBySlug(slug: string): Promise<any | null> {
    try {
      const posts = await this.api.posts.browse({
        filter: `slug:${slug}`,
        limit: 1
      });
      return posts.length > 0 ? posts[0] : null;
    } catch (error) {
      console.error('Failed to get post by slug:', error);
      throw error;
    }
  }

  async updatePost(id: string, updates: any): Promise<any> {
    try {
      return await this.api.posts.edit({ id, ...updates });
    } catch (error) {
      console.error('Failed to update post:', error);
      throw error;
    }
  }

  async createPost(postData: any): Promise<any> {
    try {
      return await this.api.posts.add(postData);
    } catch (error) {
      console.error('Failed to create post:', error);
      throw error;
    }
  }

  async deletePost(id: string): Promise<void> {
    try {
      await this.api.posts.delete({ id });
    } catch (error) {
      console.error('Failed to delete post:', error);
      throw error;
    }
  }
}

/**
 * Creates a Ghost API client using environment settings
 */
export function createGhostAPIClient(): GhostAPIClient | null {
  const envSettings = loadEnvironmentSettings();

  if (!envSettings.ghostUrl || !envSettings.ghostAdminApiKey) {
    console.log('⚠️ Ghost credentials not found in environment, skipping Ghost API operations');
    return null;
  }

  return new GhostAPIClient(envSettings.ghostUrl, envSettings.ghostAdminApiKey);
}

/**
 * Deletes all posts from the Ghost instance
 * This is intended for test cleanup only
 */
export async function deleteAllGhostPosts(): Promise<void> {
  const api = createGhostAPIClient();

  if (!api) {
    console.log('🔄 Skipping Ghost cleanup - no API credentials available');
    return;
  }

  try {
    console.log('🧹 Starting Ghost posts cleanup...');

    // Get all posts with a high limit to ensure we get everything
    const posts = await api.getPosts({ limit: 'all' });

    if (!posts || posts.length === 0) {
      console.log('✅ No posts found to delete');
      return;
    }

    console.log(`🗑️ Found ${posts.length} posts to delete`);

    // Delete posts in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < posts.length; i += batchSize) {
      const batch = posts.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async (post) => {
          try {
            await api.deletePost(post.id);
            console.log(`✅ Deleted post: ${post.title} (${post.id})`);
          } catch (error) {
            console.error(`❌ Failed to delete post ${post.title} (${post.id}):`, error.message);
          }
        })
      );

      // Small delay between batches to be respectful to the API
      if (i + batchSize < posts.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    console.log('✅ Ghost posts cleanup completed');
  } catch (error) {
    console.error('❌ Error during Ghost cleanup:', error.message);
    // Don't throw the error - cleanup failures shouldn't break tests
  }
}

/**
 * Deletes posts created during a specific test run
 * Uses title patterns to identify test posts
 */
export async function deleteTestPosts(titlePatterns: string[] = ['Test Post', 'test-post']): Promise<void> {
  const api = createGhostAPIClient();

  if (!api) {
    console.log('🔄 Skipping test posts cleanup - no API credentials available');
    return;
  }

  try {
    console.log('🧹 Starting test posts cleanup...');

    const posts = await api.getPosts({ limit: 'all' });

    if (!posts || posts.length === 0) {
      console.log('✅ No posts found');
      return;
    }

    // Filter posts that match test patterns
    const testPosts = posts.filter(post =>
      titlePatterns.some(pattern =>
        post.title?.toLowerCase().includes(pattern.toLowerCase()) ||
        post.slug?.toLowerCase().includes(pattern.toLowerCase())
      )
    );

    if (testPosts.length === 0) {
      console.log('✅ No test posts found to delete');
      return;
    }

    console.log(`🗑️ Found ${testPosts.length} test posts to delete`);

    for (const post of testPosts) {
      try {
        await api.deletePost(post.id);
        console.log(`✅ Deleted test post: ${post.title} (${post.id})`);
      } catch (error) {
        console.error(`❌ Failed to delete test post ${post.title} (${post.id}):`, error.message);
      }
    }

    console.log('✅ Test posts cleanup completed');
  } catch (error) {
    console.error('❌ Error during test posts cleanup:', error.message);
    // Don't throw the error - cleanup failures shouldn't break tests
  }
}
