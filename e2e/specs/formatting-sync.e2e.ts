import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice
} from '../helpers/shared-context';
import {
  openGhostTab,
  waitForGhostTabStatus,
  getGhostTabSyncStatus,
  syncPost
} from '../helpers/ghost-tab-helpers';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { deleteTestPosts } from '../helpers/ghost-cleanup';
import { TestPostManager, setupAndOpenTestPost } from '../helpers/ghost-api-helpers';

import { test, expect, describe, beforeEach, afterEach } from 'vitest';
import * as fs from 'fs';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Comprehensive Formatting Sync Test", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeEach(async () => {
    postManager = new TestPostManager();
  });

  afterEach(async () => {
    // Clean up managed posts
    await postManager.cleanup();

    // Also run general cleanup
    await deleteTestPosts(['Comprehensive Formatting Test']);
  });

  test("should sync comprehensive formatting from Obsidian to Ghost and back", async () => {
    const { filePath } = await setupAndOpenTestPost(
      context,
      'comprehensive-formatting-test.md'
    );

    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    const syncStatus = await getGhostTabSyncStatus(context.page);
    expect(syncStatus.isNewPost).toBe(true);
    expect(syncStatus.title).toBe('Comprehensive Formatting Test');

    await syncPost(context);

    const post = await postManager.getPostBySlug('comprehensive-formatting-test');

    expect(post).toBeTruthy();

    const updateResult = await postManager.addParagraphToPost(
      post.id,
      'This paragraph was added directly in Ghost via API.'
    );

    await context.page.waitForTimeout(2000);
    await context.page.click('.workspace-tab-header[aria-label*="Ghost"]');
    await context.page.waitForTimeout(500);

    await executeCommand(context, 'Quick switcher: Open quick switcher');
    await context.page.keyboard.type('comprehensive-formatting-test');
    await context.page.keyboard.press('Enter');
    await context.page.waitForTimeout(1000);

    await openGhostTab(context);
    await context.page.waitForSelector('.ghost-sync-status-view', { timeout: 10000 });
    await syncPost(context);

    const finalFileContent = await fs.promises.readFile(filePath, 'utf8');

    expect(finalFileContent).toContain('This paragraph was added directly in Ghost via API.');
    expect(finalFileContent).toContain('**bold text**');
    expect(finalFileContent).toContain('*italic text*');
    expect(finalFileContent).toContain('==highlighted text==');
    expect(finalFileContent).toContain('`inline code`');
    expect(finalFileContent).toContain('~~strikethrough text~~');
    expect(finalFileContent).toContain('[external link](https://obsidian.md)');
    expect(finalFileContent).toContain('- [x] Completed task');
    expect(finalFileContent).toContain('- [ ] Incomplete task');
    expect(finalFileContent).toContain('```javascript');
    expect(finalFileContent).toContain('function greetUser(name)');
    expect(finalFileContent).toContain('| Column 1 | Column 2 | Column 3 |');
    expect(finalFileContent).toContain('> This is a blockquote.');
    expect(finalFileContent).toContain('[^1]: This is the first footnote.');
    expect(finalFileContent).toContain('Slug: "comprehensive-formatting-test"');
    expect(finalFileContent).toContain('Title: "Comprehensive Formatting Test"');
  });
});
